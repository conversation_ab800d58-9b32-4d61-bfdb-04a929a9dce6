import { fileURLToPath, URL } from "node:url";
import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import VueSetupExtend from "vite-plugin-vue-setup-extend";

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, fileURLToPath(new URL("./env", import.meta.url)), "");

  const isDev = command === "serve";
  const isTest = mode === "test";
  const isStaging = mode === "staging";
  const isProd = mode === "production";

  return {
    base: "./",
    plugins: [vue(), VueSetupExtend()],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    // 指定环境变量目录
    envDir: fileURLToPath(new URL("./env", import.meta.url)),
    define: {
      // 注入环境变量到客户端代码
      __APP_ENV__: JSON.stringify(env.VITE_APP_ENV),
      __APP_TITLE__: JSON.stringify(env.VITE_APP_TITLE),
    },
    server: {
      cors: true, // 允许跨域
      host: "0.0.0.0", // 允许局域网访问
      port: isDev ? 80 : 3000,
      proxy: {
        "/local": {
          target: env.VITE_PROXY_TARGET || "https://dev.nustaronline.vip",
          changeOrigin: true,
          secure: true,
          rewrite: (path) => path.replace(/^\/local/, ""),
        },
      },
    },
    build: {
      outDir: "dist",
      sourcemap: isDev || isTest,
      minify: isProd || isStaging ? "esbuild" : false,
      rollupOptions: {
        output: {
          // 根据环境生成不同的文件名
          chunkFileNames: isProd || isStaging ? "assets/[name]-[hash].js" : "assets/[name].js",
          entryFileNames: isProd || isStaging ? "assets/[name]-[hash].js" : "assets/[name].js",
          assetFileNames: isProd || isStaging ? "assets/[name]-[hash].[ext]" : "assets/[name].[ext]",
        },
      },
    },
  };
});
